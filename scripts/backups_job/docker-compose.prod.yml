# Production Docker Compose for VPS deployment
# This assumes you have your .env file with SSH_PRIVATE_KEY configured

services:
  backup-job:
    image: oideibrett/manidae-backup:latest
    container_name: manidae-backup-job
    restart: unless-stopped
    env_file:
      - ./.env  # Load SSH_PRIVATE_KEY and other variables
    environment:
      # Repository configuration
      - REPO_URL=**************:ManidaeCloud/test-685112_syncresources.git
      - BACKUP_SOURCE_PATH=/etc/komodo
      - BACKUP_MODE=daemon
      - GIT_USER_NAME=Backup Bot
      - GIT_USER_EMAIL=<EMAIL>

      # Backup configuration
      - MAX_BACKUPS=7
      - BACKUP_MODE=backup  # Changed from daemon since we handle the loop in command
    volumes:
      # Mount the actual /etc/komodo directory from your VPS
      - /etc/komodo:/etc/komodo:ro
      # Persistent work directory
      - backup_work:/work
    # Run continuously like bandwidth monitor
    command: >
      sh -c "while true; do
        echo 'Running backup at $(date)' &&
        /usr/local/bin/backup_script.sh &&
        echo 'Backup completed. Sleeping for 6 hours...' &&
        sleep 21600;
      done"
    healthcheck:
      test: ["CMD", "pgrep", "-f", "backup_script.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  backup_work:
    driver: local
