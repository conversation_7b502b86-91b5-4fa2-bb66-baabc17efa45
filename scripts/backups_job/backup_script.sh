#!/bin/bash
set -e

# ------------------------------------------------------------
# Environment variables expected:
#   REPO_URL            - Git URL using SSH (e.g., **************:user/repo.git)
#   SSH_PRIVATE_KEY     - Private key string for this repo (from .env)
#   BACKUP_MODE         - "backup" (default), "restore", or "daemon"
#   BACKUP_SOURCE_PATH  - Path to backup (e.g., /etc/komodo) - passed via docker-compose
#   GIT_USER_NAME       - Git user name for commits - passed via docker-compose
#   GIT_USER_EMAIL      - Git user email for commits - passed via docker-compose
#   MAX_BACKUPS         - Maximum number of backup versions to keep (default: 7)
#   BACKUP_INTERVAL     - Backup interval in seconds (default: 21600 = 6 hours)
#   RESTORE_VERSION     - Version number to restore (1=latest, 2=second latest, etc.)
# ------------------------------------------------------------

WORK_DIR="/work"

# Set defaults that can be overridden by docker-compose environment
REPO_URL="${REPO_URL:-**************:ManidaeCloud/test-685112_syncresources.git}"
BACKUP_SOURCE_PATH="${BACKUP_SOURCE_PATH:-/data}"
GIT_USER_NAME="${GIT_USER_NAME:-Backup Bot}"
GIT_USER_EMAIL="${GIT_USER_EMAIL:-<EMAIL>}"
MAX_BACKUPS="${MAX_BACKUPS:-7}"
BACKUP_INTERVAL="${BACKUP_INTERVAL:-21600}"
RESTORE_VERSION="${RESTORE_VERSION:-1}"

# Validate required environment variables
if [ -z "$REPO_URL" ]; then
    error "REPO_URL environment variable is required"
fi

if [ -z "$SSH_PRIVATE_KEY" ]; then
    error "SSH_PRIVATE_KEY environment variable is required"
fi

# Extract repo name (last part of URL, stripped of .git)
REPO_NAME=$(basename "$REPO_URL" .git | tr '[:upper:]' '[:lower:]')
REPO_WORK="$WORK_DIR/repo-$REPO_NAME"
KEY_FILE="$HOME/.ssh/id_ed25519-$REPO_NAME"
SSH_CONFIG="$HOME/.ssh/config"

# Helper logging
info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1" >&2; exit 1; }

# ------------------------------------------------------------
# Setup SSH key and config
# ------------------------------------------------------------
setup_ssh() {
  mkdir -p ~/.ssh
  chmod 700 ~/.ssh

  # Write the SSH private key to file
  # Add BEGIN/END markers if they're missing (new format from upstream)
  if [[ "$SSH_PRIVATE_KEY" != *"-----BEGIN"* ]]; then
    # New format: just the key content without markers
    echo "-----BEGIN OPENSSH PRIVATE KEY-----" > "$KEY_FILE"
    echo "$SSH_PRIVATE_KEY" | fold -w 64 >> "$KEY_FILE"
    echo "-----END OPENSSH PRIVATE KEY-----" >> "$KEY_FILE"
  else
    # Old format: already has markers
    echo "$SSH_PRIVATE_KEY" > "$KEY_FILE"
  fi
  chmod 600 "$KEY_FILE"

  # Add GitHub to known hosts
  ssh-keyscan github.com >> ~/.ssh/known_hosts 2>/dev/null || true
  chmod 644 ~/.ssh/known_hosts

  # Create SSH config for this specific repo
  cat >> "$SSH_CONFIG" <<EOF
Host github-$REPO_NAME
    HostName github.com
    User git
    IdentityFile $KEY_FILE
    IdentitiesOnly yes
    StrictHostKeyChecking accept-new
    UserKnownHostsFile ~/.ssh/known_hosts

EOF
  chmod 600 "$SSH_CONFIG"

  info "SSH key for $REPO_NAME configured."
}

# ------------------------------------------------------------
# Setup Git configuration
# ------------------------------------------------------------
setup_git() {
  git config --global user.name "$GIT_USER_NAME"
  git config --global user.email "$GIT_USER_EMAIL"
  git config --global init.defaultBranch main
  info "Git configuration set: $GIT_USER_NAME <$GIT_USER_EMAIL>"
}

# ------------------------------------------------------------
# Backup logic
# ------------------------------------------------------------
# ------------------------------------------------------------
# Backup rotation functions
# ------------------------------------------------------------
rotate_backups() {
  local base_name=$(basename "$BACKUP_SOURCE_PATH")
  local backup_base_dir="$REPO_WORK/backups"

  cd "$REPO_WORK"

  # Get current timestamp for the new backup
  local timestamp=$(date '+%Y%m%d_%H%M%S')

  # Create backup directories if they don't exist
  mkdir -p "$backup_base_dir"

  # Create the new backup with actual timestamp
  local new_backup_dir="$backup_base_dir/${base_name}_${timestamp}"
  mkdir -p "$new_backup_dir"

  info "Creating new backup: $(basename "$new_backup_dir")"

  # Copy the source to the new backup directory
  if [ -d "$BACKUP_SOURCE_PATH" ]; then
    cp -rf "$BACKUP_SOURCE_PATH"/. "$new_backup_dir/"
  else
    cp -f "$BACKUP_SOURCE_PATH" "$new_backup_dir/"
  fi

  # Find all existing backups (sorted by name, which sorts by timestamp)
  local existing_backups=($(find "$backup_base_dir" -maxdepth 1 -name "${base_name}_*" -type d | sort))
  local backup_count=${#existing_backups[@]}

  info "Total backups after creation: $backup_count (max allowed: $MAX_BACKUPS)"

  # If we have more than MAX_BACKUPS, remove the oldest ones
  if [ $backup_count -gt $MAX_BACKUPS ]; then
    local excess_count=$((backup_count - MAX_BACKUPS))
    info "Removing $excess_count oldest backup(s)"

    for ((i=0; i<$excess_count; i++)); do
      local old_backup=${existing_backups[$i]}
      info "Removing old backup: $(basename "$old_backup")"
      rm -rf "$old_backup"
    done
  fi

  return 0
}

run_backup() {
  # Update the REPO_URL to use our SSH alias
  REPO_SSH_URL="github-$REPO_NAME:ManidaeCloud/test-685112_syncresources.git"

  if [ ! -d "$REPO_WORK/.git" ]; then
    # Clean up any existing directory that's not a git repo
    if [ -d "$REPO_WORK" ]; then
      info "Removing existing non-git directory: $REPO_WORK"
      rm -rf "$REPO_WORK"
    fi

    info "Cloning repository into $REPO_WORK..."
    git clone "$REPO_SSH_URL" "$REPO_WORK"
  fi

  cd "$REPO_WORK"

  info "Pulling latest changes..."
  git pull origin main || true

  # Check if backup source path exists
  if [ ! -d "$BACKUP_SOURCE_PATH" ] && [ ! -f "$BACKUP_SOURCE_PATH" ]; then
    error "Backup source path does not exist: $BACKUP_SOURCE_PATH"
  fi

  info "Creating rotated backup from $BACKUP_SOURCE_PATH..."

  # Perform backup rotation
  rotate_backups

  git add .
  if git status --porcelain | grep -q .; then
    git commit -m "Automated backup: $(date) - $(basename "$BACKUP_SOURCE_PATH") (keeping $MAX_BACKUPS most recent)"
    git push origin main
    info "Backup successful."
  else
    info "No changes to commit."
  fi
}

# ------------------------------------------------------------
# Restore logic
# ------------------------------------------------------------
list_backups() {
  local base_name=$(basename "$BACKUP_SOURCE_PATH")
  local backup_base_dir="$REPO_WORK/backups"

  cd "$REPO_WORK"
  git pull origin main >/dev/null 2>&1 || true

  info "Available backups for $base_name:"

  # Find existing backups (sorted by timestamp, newest first)
  local existing_backups=($(find "$backup_base_dir" -maxdepth 1 -name "${base_name}_*" -type d | sort -r))

  if [ ${#existing_backups[@]} -eq 0 ]; then
    info "No backups found."
    return 1
  fi

  for ((i=0; i<${#existing_backups[@]}; i++)); do
    local backup_path=${existing_backups[$i]}
    local backup_name=$(basename "$backup_path")
    local timestamp=$(echo "$backup_name" | sed "s/${base_name}_//")
    local version_num=$((i + 1))

    # Format timestamp for display
    local year=${timestamp:0:4}
    local month=${timestamp:4:2}
    local day=${timestamp:6:2}
    local hour=${timestamp:9:2}
    local minute=${timestamp:11:2}
    local second=${timestamp:13:2}
    local formatted_date="$year-$month-$day $hour:$minute:$second"

    info "  Version $version_num (latest): $backup_name"
    info "    Created: $formatted_date"
  done

  return 0
}

run_restore() {
  # Update the REPO_URL to use our SSH alias
  REPO_SSH_URL="github-$REPO_NAME:ManidaeCloud/test-685112_syncresources.git"

  if [ ! -d "$REPO_WORK/.git" ]; then
    info "Cloning repository for restore..."
    git clone "$REPO_SSH_URL" "$REPO_WORK"
  fi

  cd "$REPO_WORK"
  git fetch origin main
  git checkout main

  local base_name=$(basename "$BACKUP_SOURCE_PATH")
  local backup_base_dir="$REPO_WORK/backups"

  # Find all backups sorted by timestamp (newest first)
  local existing_backups=($(find "$backup_base_dir" -maxdepth 1 -name "${base_name}_*" -type d | sort -r))

  if [ ${#existing_backups[@]} -eq 0 ]; then
    info "No backups found. Available backups:"
    list_backups
    error "Restore failed: No backups exist"
  fi

  # Check if requested version exists
  if [ $RESTORE_VERSION -lt 1 ] || [ $RESTORE_VERSION -gt ${#existing_backups[@]} ]; then
    info "Backup version $RESTORE_VERSION not found. Available versions:"
    list_backups
    error "Restore failed: Version $RESTORE_VERSION does not exist (valid range: 1-${#existing_backups[@]})"
  fi

  # Get the backup directory (array is 0-indexed, but versions are 1-indexed)
  local backup_dir=${existing_backups[$((RESTORE_VERSION - 1))]}

  info "Restoring version $RESTORE_VERSION from $(basename "$backup_dir") to $BACKUP_SOURCE_PATH..."

  # Create the target directory if it doesn't exist
  mkdir -p "$(dirname "$BACKUP_SOURCE_PATH")"

  # Backup current data before restore
  if [ -d "$BACKUP_SOURCE_PATH" ]; then
    local backup_current="$BACKUP_SOURCE_PATH.backup.$(date +%Y%m%d_%H%M%S)"
    info "Backing up current data to $backup_current"
    cp -rf "$BACKUP_SOURCE_PATH" "$backup_current" 2>/dev/null || true
  fi

  # Clear existing data and restore from backup
  # Use find to remove contents instead of rm -rf to handle mounted directories
  if [ -d "$BACKUP_SOURCE_PATH" ]; then
    find "$BACKUP_SOURCE_PATH" -mindepth 1 -delete 2>/dev/null || {
      info "Could not clear directory, copying over existing files..."
    }
  else
    mkdir -p "$BACKUP_SOURCE_PATH"
  fi

  cp -rf "$backup_dir"/. "$BACKUP_SOURCE_PATH/"

  info "Restore complete. Version $RESTORE_VERSION restored to $BACKUP_SOURCE_PATH"
}

# ------------------------------------------------------------
# Daemon mode - continuous backup service
# ------------------------------------------------------------
run_daemon() {
  info "Starting backup daemon..."
  info "Backup interval: $BACKUP_INTERVAL seconds ($(($BACKUP_INTERVAL / 3600)) hours)"
  info "Maximum backup versions: $MAX_BACKUPS"

  while true; do
    info "Running scheduled backup at $(date)"

    # Run backup in a subshell to handle any errors
    if run_backup; then
      info "Backup completed successfully"
    else
      info "Backup failed, will retry on next interval"
    fi

    info "Next backup in $BACKUP_INTERVAL seconds ($(($BACKUP_INTERVAL / 3600)) hours)..."
    sleep "$BACKUP_INTERVAL"
  done
}

# ------------------------------------------------------------
# List available backups
# ------------------------------------------------------------
run_list() {
  # Update the REPO_URL to use our SSH alias
  REPO_SSH_URL="github-$REPO_NAME:ManidaeCloud/test-685112_syncresources.git"

  if [ ! -d "$REPO_WORK/.git" ]; then
    info "Cloning repository to list backups..."
    git clone "$REPO_SSH_URL" "$REPO_WORK"
  fi

  cd "$REPO_WORK"
  list_backups
}

# ------------------------------------------------------------
# Main
# ------------------------------------------------------------
info "Starting backup script..."
info "Repository: $REPO_URL"
info "Backup source: $BACKUP_SOURCE_PATH"
info "Mode: ${BACKUP_MODE:-backup}"

# Handle special case for help
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  echo "Manidae Backup Script"
  echo ""
  echo "Environment Variables:"
  echo "  BACKUP_MODE=backup|restore|daemon|list"
  echo "  BACKUP_SOURCE_PATH=/path/to/backup (default: /data)"
  echo "  MAX_BACKUPS=7 (number of versions to keep)"
  echo "  BACKUP_INTERVAL=21600 (seconds between backups in daemon mode)"
  echo "  RESTORE_VERSION=1 (version to restore, 1=latest)"
  echo ""
  echo "Docker exec commands:"
  echo "  # List available backups"
  echo "  docker exec container-name /usr/local/bin/backup_script.sh list"
  echo ""
  echo "  # Restore version 3"
  echo "  docker exec -e RESTORE_VERSION=3 container-name /usr/local/bin/backup_script.sh restore"
  echo ""
  echo "  # Manual backup"
  echo "  docker exec container-name /usr/local/bin/backup_script.sh backup"
  exit 0
fi

setup_ssh
setup_git

case "${BACKUP_MODE:-backup}" in
  backup) run_backup ;;
  restore) run_restore ;;
  daemon) run_daemon ;;
  list) run_list ;;
  *) error "Unknown BACKUP_MODE: $BACKUP_MODE (must be 'backup', 'restore', 'daemon', or 'list')" ;;
esac

info "Script completed successfully."
