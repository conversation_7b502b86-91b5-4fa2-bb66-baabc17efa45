# Manidae Cloud Backup Service

A Docker-based backup solution that automatically backs up specified directories to a GitHub repository using SSH authentication with backup rotation and restore capabilities.

## Features

- **Automated backup to GitHub repository** with SSH key authentication
- **Backup rotation** - Keep configurable number of backup versions (default: 7)
- **Daemon mode** - Continuous backup service like bandwidth monitoring
- **Restore functionality** - Restore any backup version with simple commands
- **Docker containerized** for easy deployment
- **Comprehensive logging** and error handling
- **Easy management** with helper scripts

## Quick Start

### 1. Local Testing

Test the backup functionality locally:

```bash
cd scripts/backups_job

# Test basic backup functionality
./test_backup.sh

# Test backup rotation and restore functionality
./test_rotation.sh
```

This will:
- Build the Docker image
- Run a test backup using your test data
- Push the backup to your GitHub repository

### 2. Build and Push to Docker Hub

```bash
# Set your Docker Hub username
export DOCKER_USERNAME=your-dockerhub-username

# Run the build and push script
./build_and_push.sh
```

Or manually:
```bash
# Build the image
docker build -t your-dockerhub-username/manidae-backup:latest .

# Push to Docker Hub
docker push your-dockerhub-username/manidae-backup:latest
```

### 3. Deploy on VPS

1. Copy `docker-compose.prod.yml` to your VPS
2. Create a `.env` file with your SSH private key:

```bash
# .env file on your VPS
# Note: Just the key content without BEGIN/END markers (new format)
SSH_PRIVATE_KEY=b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtz...
```

**Note:** The script automatically adds the `-----BEGIN OPENSSH PRIVATE KEY-----` and `-----END OPENSSH PRIVATE KEY-----` markers, so only include the base64 key content in the environment variable.

3. Update the image name in `docker-compose.prod.yml`
4. Start the service:

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `REPO_URL` | GitHub repository SSH URL | `**************:ManidaeCloud/test-685112_syncresources.git` |
| `BACKUP_SOURCE_PATH` | Directory to backup | `/etc/komodo` |
| `BACKUP_MODE` | `backup`, `restore`, `daemon`, or `list` | `backup` |
| `MAX_BACKUPS` | Number of backup versions to keep | `7` |
| `BACKUP_INTERVAL` | Backup interval in seconds (daemon mode) | `21600` (6 hours) |
| `RESTORE_VERSION` | Version to restore (1=latest, 2=second latest, etc.) | `1` |
| `GIT_USER_NAME` | Git commit author name | `Backup Bot` |
| `GIT_USER_EMAIL` | Git commit author email | `<EMAIL>` |
| `SSH_PRIVATE_KEY` | SSH private key content (without BEGIN/END markers) | Required |

### Backup Schedule

The production configuration runs in daemon mode with backups every 6 hours. To change this, modify the `BACKUP_INTERVAL` environment variable:

- 1 hour: `BACKUP_INTERVAL=3600`
- 6 hours: `BACKUP_INTERVAL=21600`
- 12 hours: `BACKUP_INTERVAL=43200`
- 24 hours: `BACKUP_INTERVAL=86400`

## Repository Structure

Backups are stored in the repository with version rotation:
```
backups/
├── komodo_v01_20250921_120630/  # Latest backup (version 1)
│   ├── config.toml
│   ├── docker-compose.yml
│   └── ... (other files from /etc/komodo)
├── komodo_v02_20250921_120558/  # Second latest (version 2)
│   ├── config.toml
│   └── docker-compose.yml
└── komodo_v03_20250921_120547/  # Third latest (version 3)
    ├── config.toml
    └── docker-compose.yml
```

## Backup Management

### Using the Helper Script

The `backup_commands.sh` script provides easy management with configurable container names:

```bash
# Default container name (manidae-backup-job)
./backup_commands.sh list
./backup_commands.sh restore 1
./backup_commands.sh backup
./backup_commands.sh status
./backup_commands.sh logs

# Custom container name as argument
./backup_commands.sh my-backup-container list
./backup_commands.sh my-backup-container restore 3
./backup_commands.sh my-backup-container backup

# Custom container name via environment variable
CONTAINER_NAME=my-backup-service ./backup_commands.sh list
CONTAINER_NAME=my-backup-service ./backup_commands.sh restore 1
```

### Direct Docker Commands

```bash
# List available backups
docker exec manidae-backup-job env BACKUP_MODE=list /usr/local/bin/backup_script.sh

# Restore version 2
docker exec manidae-backup-job env BACKUP_MODE=restore RESTORE_VERSION=2 /usr/local/bin/backup_script.sh

# Manual backup
docker exec manidae-backup-job env BACKUP_MODE=backup /usr/local/bin/backup_script.sh
```

## Monitoring

Check backup logs:
```bash
docker logs manidae-backup-job
```

View backup status:
```bash
docker ps | grep backup
```

## Troubleshooting

### SSH Key Issues
- Ensure your SSH private key has access to the repository
- The script expects just the key content without `-----BEGIN/END-----` markers
- The script automatically adds the proper OpenSSH format markers
- Verify the repository URL is correct

### Permission Issues
- Ensure the backup source directory is readable
- Check Docker volume mounts are correct

### Git Issues
- Verify the repository exists and is accessible
- Check that the main branch exists in the repository

## Manual Operations

### One-time Backup
```bash
docker run --rm \
    --env-file .env \
    -e BACKUP_MODE=backup \
    -v /etc/komodo:/etc/komodo:ro \
    your-dockerhub-username/manidae-backup:latest
```

### Restore from Backup
```bash
# Restore latest backup (version 1)
docker run --rm \
    --env-file .env \
    -e BACKUP_MODE=restore \
    -e RESTORE_VERSION=1 \
    -v /etc/komodo:/etc/komodo \
    your-dockerhub-username/manidae-backup:latest

# Restore specific version (e.g., version 3)
docker run --rm \
    --env-file .env \
    -e BACKUP_MODE=restore \
    -e RESTORE_VERSION=3 \
    -v /etc/komodo:/etc/komodo \
    your-dockerhub-username/manidae-backup:latest
```

### List Available Backups
```bash
docker run --rm \
    --env-file .env \
    -e BACKUP_MODE=list \
    your-dockerhub-username/manidae-backup:latest
```

## Security Notes

- SSH private keys are only stored in environment variables, never in the Docker image
- The script automatically formats SSH keys with proper BEGIN/END markers for security
- The backup source is mounted read-only for safety
- All Git operations use SSH authentication
- Container runs with minimal privileges
